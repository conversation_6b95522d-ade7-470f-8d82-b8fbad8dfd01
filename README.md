# 企业级 React 项目

本项目基于 Vite + React + TypeScript，集成了 ESLint、Prettier、Jest、<PERSON><PERSON>、<PERSON><PERSON><PERSON><PERSON>、环境变量管理、企业级目录结构，适合大型商业项目开发。

## 主要特性

- Vite 极速开发环境
- TypeScript 类型安全
- ESLint + Prettier 代码规范
- Jest 单元测试
- <PERSON><PERSON> + Commitlint 提交规范
- lint-staged 提交前自动修复
- 目录结构：src/components、hooks、utils、assets、pages、api
- 环境变量管理（.env.example）

## 快速开始

```bash
npm run dev
```

## 代码规范

- 提交前自动执行 lint 和 prettier
- 提交信息需符合 conventional 规范

## 测试

```bash
npm test
```

## 目录结构

```
src/
  components/
  hooks/
  utils/
  assets/
  pages/
  api/
```

## 环境变量

请参考 `.env.example` 文件。

# React + TypeScript + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type-aware lint rules:

```js
export default tseslint.config([
  globalIgnores(['dist']),
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      // Other configs...

      // Remove tseslint.configs.recommended and replace with this
      ...tseslint.configs.recommendedTypeChecked,
      // Alternatively, use this for stricter rules
      ...tseslint.configs.strictTypeChecked,
      // Optionally, add this for stylistic rules
      ...tseslint.configs.stylisticTypeChecked,

      // Other configs...
    ],
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.node.json', './tsconfig.app.json'],
        tsconfigRootDir: import.meta.dirname,
      },
      // other options...
    },
  },
]);
```

You can also install [eslint-plugin-react-x](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-x) and [eslint-plugin-react-dom](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-dom) for React-specific lint rules:

```js
// eslint.config.js
import reactX from 'eslint-plugin-react-x';
import reactDom from 'eslint-plugin-react-dom';

export default tseslint.config([
  globalIgnores(['dist']),
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      // Other configs...
      // Enable lint rules for React
      reactX.configs['recommended-typescript'],
      // Enable lint rules for React DOM
      reactDom.configs.recommended,
    ],
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.node.json', './tsconfig.app.json'],
        tsconfigRootDir: import.meta.dirname,
      },
      // other options...
    },
  },
]);
```
