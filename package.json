{"name": "react-projects", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "husky install"}, "dependencies": {"react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.35.0", "@rollup/rollup-win32-x64-msvc": "^4.50.2", "@testing-library/jest-dom": "^6.8.0", "@types/jest": "^30.0.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "@vitejs/plugin-react": "^5.0.0", "cross-env": "^10.0.0", "eslint": "^9.35.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "husky": "^8.0.0", "jest": "^30.1.3", "jiti": "^2.5.1", "lint-staged": "^16.1.6", "prettier": "^3.6.2", "rollup": "^4.50.2", "ts-jest": "^29.4.2", "typescript": "~5.8.3", "typescript-eslint": "^8.44.0", "vite": "^7.1.2"}}