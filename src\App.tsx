import { useState } from 'react'
import './App.css'

function App() {
  const [count, setCount] = useState(0)

  const autoSelfIncreasing = () => {
    setCount((count) => count + 1)
  }

  const [form, setForm] = useState({
    name: 'eric',
    age: 0,
  })

  return (
    <>

      <div>
        <input
          type="text"
          value={form.name}
          onChange={(e) => setForm({ ...form, name: e.target.value })}
        />
      </div>
      <div>
      <button onClick={autoSelfIncreasing}>计数器Button：{count}</button>
      </div>

<div>
      <button onClick={() => setForm({ ...form, name: 'jason' })}>修改名称：{form.name}</button>
</div>
    </>
  )
}

export default App
